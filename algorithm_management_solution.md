# 算法产品化管理方案

## 一、背景与目标

### 1.1 现状分析
- 主要业务：人员安全、电力巡检算法落地
- 算法分级：
  - 一级：参与算法评测的算法（产品化管理）
    - 已参与评级测试
    - 未参与评级测试（Atlas平台）
  - 二级：参与CMMI组件立项，但未达到较高成熟度的算法（产品化管理）
  - 三级：使用频率较低（项目级管理）
  - 四级：项目特定需求（项目级管理）
- 硬件平台：
  - x86通用服务器
  - Atlas服务器

### 1.2 现存问题
1. 代码重复与维护成本
   - 每个算法在多个项目中重复
   - 维护成本高
   - 版本管理混乱

2. 部署环境不一致
   - 本地测试与生产环境差异
   - 容器环境配置不同步
   - 模型与代码版本不匹配

### 1.3 目标
1. 建立统一的算法产品管理体系
2. 实现标准化部署流程
3. 降低维护成本
4. 提高部署效率
5. 保证产品质量

## 二、解决方案

### 2.1 算法产品化管理

#### 2.1.1 GitLab代码仓库管理
1. 仓库结构
```
# 仓库一：rated-algorithms（已评级算法产品组件）
rated-algorithms/
├── algorithm-a/
│   ├── src/           # 源代码
│   │   ├── analyse.json    # 算法参数配置
│   │   └── ...            # 其他源代码文件
│   ├── Dockerfile     # 容器配置
│   ├── .dockerignore  # Docker构建忽略文件
│   ├── scripts/       # 启动脚本
│   │   └── start.sh        # 算法启动脚本
│   ├── docs/          # 文档目录（不包含在镜像中）
│   │   ├── README.md      # 使用文档
│   │   └── test-reports/  # 评级测试报告
│   └── .gitignore    # Git忽略文件
└── algorithm-b/

# 仓库二：unrated-algorithms（未评级算法产品组件）
unrated-algorithms/
├── x86/              # x86平台算法
│   ├── algorithm-e/
│   │   ├── src/           # 源代码
│   │   │   ├── analyse.json    # 算法参数配置
│   │   │   └── ...            # 其他源代码文件
│   │   ├── Dockerfile     # 容器配置
│   │   ├── .dockerignore  # Docker构建忽略文件
│   │   ├── scripts/       # 启动脚本
│   │   │   └── start.sh        # 算法启动脚本
│   │   ├── docs/          # 文档目录（不包含在镜像中）
│   │   │   ├── README.md      # 使用文档
│   │   │   └── test-reports/  # 测试报告
│   │   └── .gitignore    # Git忽略文件
│   └── algorithm-f/
└── atlas/            # Atlas平台算法
    ├── algorithm-c/
    ├── algorithm-d/
    ├── algorithm-g/
    └── algorithm-h/
```

2. 容器自动运行机制
   - 容器启动流程
     1. 容器启动时自动执行start.sh
     2. start.sh启动算法服务
   
   - 配置文件管理
     1. analyse.json：算法参数配置文件
        - 模型参数
        - 推理参数
        - 性能参数
     2. 配置文件位置：src/analyse.json
     3. 配置文件修改：直接修改json文件

   - 参数配置说明
     1. 算法参数
        - 模型参数
        - 推理参数

3. 镜像构建优化
   - .dockerignore配置
     ```
     # 忽略文档目录
     docs/
     # 忽略测试报告
     **/test-reports/
     # 忽略Git相关文件
     .git/
     .gitignore
     # 忽略其他不需要的文件
     *.md
     *.txt
     ```
   
   - 镜像分层优化
     1. 基础层：系统依赖
     2. 应用层：算法代码
     3. 配置层：配置文件
     4. 脚本层：启动脚本


4. GitLab管理规范
   - 分支管理策略
     - main：主分支，保持稳定
     - develop：开发分支
     - feature/*：功能分支
     - release/*：发布分支
     - hotfix/*：紧急修复分支
   
   - 权限管理
     - 算法开发人员
       - 对应算法仓库的开发权限
       - 对应算法镜像的读写权限
       - 文档更新权限
     - 其他人员
       - 所有仓库的只读权限
       - 所有镜像的只读权限
       - 文档查看权限

   - 代码审查
     - 强制代码审查
     - 自行测试

#### 2.1.2 算法管理策略
1. 已评级算法产品（rated-algorithms仓库）
   - 统一代码仓库
   - 标准化发布流程
   - 容器化部署
   - 版本控制
   - 性能监控
   - 评级报告管理
   - 性能指标跟踪
   - 定期复测
   - 参数配置管理
   - 自动运行机制

2. 未评级算法产品（unrated-algorithms仓库）
   - 统一代码仓库
   - 标准化发布流程
   - 容器化部署
   - 版本控制
   - 性能监控
   - 评级计划管理
   - 性能预评估
   - CMMI组件管理
   - 参数配置管理
   - 自动运行机制

3. 项目级管理（三级和四级算法）
   - 随项目代码管理
   - 项目内版本控制
   - 项目特定部署
   - 项目内文档管理

#### 2.1.3 版本管理
- 采用语义化版本号（Semantic Versioning）
- 版本组成：
  - 代码版本
  - 模型版本
  - 容器镜像版本
  - 更新日志
  - 性能指标
  - 硬件平台标识
  - 评测状态
    - 已评级：评级结果
    - 未评级：计划评级时间
  - CMMI评级

#### 2.1.4 发布流程
1. 月度发布计划
2. 自动化测试
3. 版本打包
4. 镜像构建
5. 发布通知

### 2.3 文档管理

#### 2.3.1 文档类型
- 算法说明文档
- 部署文档
- 使用示例
- 常见问题解答

#### 2.3.2 文档更新机制
- 版本同步更新
- 定期审查
- 用户反馈收集